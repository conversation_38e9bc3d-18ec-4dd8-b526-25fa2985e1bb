# Zustand Migration Guide

## 🎉 Migration Complete!

Dự án đã được migration thành công từ React Context API sang Zustand cho global state management.

## 📋 Những gì đã thay đổi

### 1. **Cài đặt Zustand**
```bash
yarn add zustand
```

### 2. **Thay thế React Context bằng Zustand Store**

**Trước (React Context):**
```typescript
// useRoot.tsx
const RootContext = createContext({} as RootProps);
const RootProvider = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  // ... logic
  return <RootContext.Provider value={value}>{children}</RootContext.Provider>;
};
```

**Sau (Zustand):**
```typescript
// useRoot.tsx
export const useRootStore = create<RootStore>()(
  devtools((set, get) => ({
    isLoading: true,
    shopInfo: null,
    error: false,
    initializeRoot: async () => { /* logic */ },
    // ... actions
  }))
);
```

### 3. **Cập nhật Provider Pattern**

**Trước:**
```typescript
const Provider = ({ children }) => {
  return <RootProvider>{children}</RootProvider>;
};
```

**Sau:**
```typescript
const Provider = ({ children }) => {
  return <RootInitializer>{children}</RootInitializer>;
};
```

### 4. **Cách sử dụng mới**

#### **Backward Compatibility (tương thích ngược):**
```typescript
// Vẫn hoạt động như cũ
const { isLoading, shopInfo, error } = useRoot();
```

#### **Optimized Selectors (hiệu suất tốt hơn):**
```typescript
// Chỉ re-render khi isLoading thay đổi
const isLoading = useIsLoading();
const shopInfo = useShopInfo();
const error = useError();
```

#### **Actions:**
```typescript
const { initializeRoot, setLoading, reset } = useRootActions();
```

## 🚀 Lợi ích của Zustand

### ✅ **Performance**
- Automatic optimization và minimal re-renders
- Selective subscriptions (chỉ re-render khi cần thiết)

### ✅ **Developer Experience**
- Không cần Provider wrapper
- Redux DevTools support
- TypeScript support tuyệt vời
- API đơn giản hơn

### ✅ **Bundle Size**
- Zustand chỉ ~2.5kb (gzipped)
- Nhỏ hơn nhiều so với Redux

### ✅ **Flexibility**
- Có thể sử dụng outside React components
- Dễ dàng test
- Không cần reducers hay complex setup

## 🛠 Cách sử dụng

### **1. Lấy state:**
```typescript
import { useIsLoading, useShopInfo, useError } from '../hooks/useRoot';

const MyComponent = () => {
  const isLoading = useIsLoading();
  const shopInfo = useShopInfo();
  const error = useError();
  
  // Component logic
};
```

### **2. Thực hiện actions:**
```typescript
import { useRootActions } from '../hooks/useRoot';

const MyComponent = () => {
  const { initializeRoot, setLoading, reset } = useRootActions();
  
  const handleReset = () => {
    reset();
  };
  
  const handleRefresh = () => {
    initializeRoot();
  };
};
```

### **3. Sử dụng store trực tiếp (outside React):**
```typescript
import { useRootStore } from '../hooks/useRoot';

// Lấy state
const currentState = useRootStore.getState();

// Subscribe to changes
const unsubscribe = useRootStore.subscribe((state) => {
  console.log('State changed:', state);
});

// Cleanup
unsubscribe();
```

## 🔧 DevTools

Zustand store đã được cấu hình với Redux DevTools. Mở browser DevTools và tìm tab "Redux" để debug state changes.

## 📁 File Structure

```
src/dashboard/
├── hooks/
│   └── useRoot.tsx          # Zustand store + hooks
├── components/
│   ├── RootInitializer.tsx  # Khởi tạo store
│   ├── ShopInfoDisplay.tsx  # Demo component
│   └── home/
│       └── index.tsx        # Updated với Zustand
└── provider/
    └── index.tsx            # Updated provider
```

## 🧪 Testing

Để test ứng dụng:

```bash
# Type checking
yarn typecheck

# Start development server
yarn dev
```

## 🔄 Backward Compatibility

Migration này hoàn toàn backward compatible. Tất cả code hiện tại sử dụng `useRoot()` hook vẫn hoạt động bình thường.

## 🎯 Next Steps

1. **Optimize components**: Thay thế `useRoot()` bằng selective hooks như `useIsLoading()`, `useShopInfo()`
2. **Add more stores**: Tạo thêm Zustand stores cho các features khác
3. **Add middleware**: Thêm persistence, logging, hoặc các middleware khác nếu cần

## 📚 Resources

- [Zustand Documentation](https://github.com/pmndrs/zustand)
- [Zustand Best Practices](https://github.com/pmndrs/zustand/wiki/Best-Practices)
- [TypeScript Guide](https://github.com/pmndrs/zustand#typescript)
