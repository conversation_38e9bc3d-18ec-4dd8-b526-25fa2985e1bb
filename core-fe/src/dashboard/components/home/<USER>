import { dashboard } from "@wix/dashboard";
import * as Icons from "@wix/wix-ui-icons-common";
import React, { FC } from "react";

import {
  Box,
  Button,
  EmptyState,
  Image,
  Page,
  TextButton,
  WixDesignSystemProvider
} from "@wix/design-system";
import "@wix/design-system/styles.global.css";

import ShopInfoDisplay from "../ShopInfoDisplay";
import ZustandDemo from "../ZustandDemo";

const Home: FC = () => {
  return (
    <WixDesignSystemProvider features={{ newColorsBranding: true }}>
      <Page>
        <Page.Header
          title='Dashboard Page'
          subtitle='Add management capabilities to your app.'
          actionsBar={
            <Button
              onClick={() => {
                dashboard.showToast({
                  message: "Your first toast message!"
                });
              }}
              prefixIcon={<Icons.GetStarted />}
            >
              Show a toast
            </Button>
          }
        />
        <Page.Content>
          <Box direction='vertical' gap={4}>
            {/* Advanced Zustand Demo */}
            <ZustandDemo />

            {/* Shop Information Display using Zustand */}
            <ShopInfoDisplay />

            {/* Original Empty State */}
            <EmptyState
              image={<Image fit='contain' height='100px' transparent />}
              title='Zustand State Management Demo'
              subtitle='This page now uses Zustand for global state management instead of React Context.'
              theme='page'
            >
              <TextButton
                as='a'
                href='https://dev.wix.com/docs/build-apps/develop-your-app/frameworks/wix-cli/supported-extensions/dashboard-extensions/dashboard-pages/add-dashboard-page-extensions-with-the-cli#add-dashboard-page-extensions-with-the-cli'
                target='_blank'
                prefixIcon={<Icons.ExternalLink />}
              >
                Dashboard pages documentation
              </TextButton>
            </EmptyState>
          </Box>
        </Page.Content>
      </Page>
    </WixDesignSystemProvider>
  );
};

export default Home;
