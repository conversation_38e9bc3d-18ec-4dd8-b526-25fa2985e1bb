import React, { useEffect, type FC, type ReactNode } from "react";

import { useRootActions } from "../hooks/useRoot";

interface RootInitializerProps {
  children: ReactNode;
}

/**
 * Component that initializes the root store when the app starts
 * This replaces the old RootProvider pattern
 */
const RootInitializer: FC<RootInitializerProps> = ({ children }) => {
  const { initializeRoot } = useRootActions();

  useEffect(() => {
    initializeRoot();
  }, [initializeRoot]);

  return <>{children}</>;
};

export default RootInitializer;
