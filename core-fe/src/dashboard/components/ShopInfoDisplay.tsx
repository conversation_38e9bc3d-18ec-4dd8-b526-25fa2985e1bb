import React, { type FC } from "react";

import { Box, Text, Loader, Card } from "@wix/design-system";

import { useRoot, useIsLoading, useShopInfo, useError, useRootActions } from "../hooks/useRoot";

/**
 * Example component demonstrating different ways to use the Zustand store
 */
const ShopInfoDisplay: FC = () => {
  // Method 1: Using the backward compatibility hook (same as before)
  const { isLoading: isLoadingCompat, shopInfo: shopInfoCompat, error: errorCompat } = useRoot();

  // Method 2: Using individual selectors (better performance)
  const isLoading = useIsLoading();
  const shopInfo = useShopInfo();
  const error = useError();

  // Method 3: Using actions
  const { reset, initializeRoot } = useRootActions();

  if (isLoading) {
    return (
      <Box align="center" verticalAlign="middle" height="200px">
        <Loader />
        <Text>Loading shop information...</Text>
      </Box>
    );
  }

  if (error) {
    return (
      <Card>
        <Card.Content>
          <Text color="D10">Error loading shop information</Text>
          <Box marginTop={2}>
            <button onClick={() => initializeRoot()}>Retry</button>
          </Box>
        </Card.Content>
      </Card>
    );
  }

  if (!shopInfo) {
    return (
      <Card>
        <Card.Content>
          <Text>No shop information available</Text>
        </Card.Content>
      </Card>
    );
  }

  return (
    <Card>
      <Card.Header title="Shop Information" />
      <Card.Content>
        <Box direction="vertical" gap={2}>
          <Box>
            <Text weight="bold">Shop ID: </Text>
            <Text>{shopInfo.shop?.id || 'N/A'}</Text>
          </Box>
          <Box>
            <Text weight="bold">Instance ID: </Text>
            <Text>{shopInfo.shop?.instanceId || 'N/A'}</Text>
          </Box>
          <Box>
            <Text weight="bold">Token: </Text>
            <Text>{shopInfo.token ? '***' : 'No token'}</Text>
          </Box>
          <Box marginTop={2}>
            <button onClick={() => reset()}>Reset Store</button>
          </Box>
        </Box>
      </Card.Content>
    </Card>
  );
};

export default ShopInfoDisplay;
