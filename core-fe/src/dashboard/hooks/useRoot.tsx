import { create } from "zustand";
import { devtools } from "zustand/middleware";

import { CommonApi } from "../../api/common.api";
import { authAxios } from "../config/axios-instance";
import { ShopInfoDto } from "../models/common/shop-data.model";

interface RootState {
  isLoading: boolean;
  shopInfo: ShopInfoDto | null;
  error: boolean;
}

interface RootActions {
  initializeRoot: () => Promise<void>;
  fetchShopInfo: (instanceId: string) => Promise<void>;
  setLoading: (loading: boolean) => void;
  setError: (error: boolean) => void;
  setShopInfo: (shopInfo: ShopInfoDto | null) => void;
  reset: () => void;
}

type RootStore = RootState & RootActions;

const initialState: RootState = {
  isLoading: true,
  shopInfo: null,
  error: false
};

export const useRootStore = create<RootStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      setLoading: (loading: boolean) => set({ isLoading: loading }, false, "setLoading"),

      setError: (error: boolean) => set({ error }, false, "setError"),

      setShopInfo: (shopInfo: ShopInfoDto | null) => set({ shopInfo }, false, "setShopInfo"),

      reset: () => set(initialState, false, "reset"),

      fetchShopInfo: async (instanceId: string) => {
        try {
          const shopData = await CommonApi.GetShop(instanceId);
          if (shopData.result) {
            get().setShopInfo(shopData.result);
            authAxios.defaults.headers.Authorization = `Bearer ${shopData.result.token}`;
          } else {
            get().setError(true);
          }
        } catch (error) {
          console.error("Error fetching shop info:", error);
          get().setError(true);
        } finally {
          get().setLoading(false);
        }
      },

      initializeRoot: async () => {
        try {
          const urlParams = new URLSearchParams(window.location.search);
          const instance = urlParams.get("instance");

          if (!instance) {
            get().setError(true);
            get().setLoading(false);
            return;
          }

          const payload = instance.split(".")[1];
          const decoded = JSON.parse(atob(payload));

          if (!decoded?.instanceId) {
            get().setError(true);
            get().setLoading(false);
            return;
          }

          await get().fetchShopInfo(decoded.instanceId);
        } catch (error) {
          console.error("Error initializing root:", error);
          get().setError(true);
          get().setLoading(false);
        }
      }
    }),
    {
      name: "root-store" // DevTools name
    }
  )
);

// Backward compatibility hook
export const useRoot = () => {
  const isLoading = useRootStore((state) => state.isLoading);
  const shopInfo = useRootStore((state) => state.shopInfo);
  const error = useRootStore((state) => state.error);

  return {
    isLoading,
    shopInfo,
    error
  };
};

// Export individual selectors for better performance
export const useIsLoading = () => useRootStore((state) => state.isLoading);
export const useShopInfo = () => useRootStore((state) => state.shopInfo);
export const useError = () => useRootStore((state) => state.error);

// Export actions
export const useRootActions = () => {
  const initializeRoot = useRootStore((state) => state.initializeRoot);
  const fetchShopInfo = useRootStore((state) => state.fetchShopInfo);
  const setLoading = useRootStore((state) => state.setLoading);
  const setError = useRootStore((state) => state.setError);
  const setShopInfo = useRootStore((state) => state.setShopInfo);
  const reset = useRootStore((state) => state.reset);

  return {
    initializeRoot,
    fetchShopInfo,
    setLoading,
    setError,
    setShopInfo,
    reset
  };
};
