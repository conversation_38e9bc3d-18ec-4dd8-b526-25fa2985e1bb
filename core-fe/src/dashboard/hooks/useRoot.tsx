import React, { ReactNode, createContext, useContext, useEffect, useState } from "react";

import { CommonApi } from "../../api/common.api";
import { authAxios } from "../config/axios-instance";
import { ShopInfoDto } from "../models/common/shop-data.model";

interface RootProps {
  isLoading: boolean;
  shopInfo: ShopInfoDto | null;
  error: boolean;
}

const RootContext = createContext({} as RootProps);

const RootProvider = ({ children }: { children: ReactNode }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [shopInfo, setShopInfo] = useState<ShopInfoDto | null>(null);
  const [error, setError] = useState<boolean>(false);

  useEffect(() => {
    const initializeRoot = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const instance = urlParams.get("instance");
      if (!instance) {
        setError(true);
        return;
      }
      const payload = instance.split(".")[1];
      const decoded = JSON.parse(atob(payload));
      if (!decoded?.instanceId) {
        setError(true);
        return;
      }
      fetchShopInfo(decoded?.instanceId);
    };

    const fetchShopInfo = async (instanceId: string) => {
      try {
        const shopData = await CommonApi.GetShop(instanceId);
        if (shopData.result) {
          setShopInfo(shopData.result);
          authAxios.defaults.headers.Authorization = `Bearer ${shopData.result.token}`;
        } else {
          setError(true);
        }
      } finally {
        setIsLoading(false);
      }
    };

    initializeRoot();
  }, []);

  const value: RootProps = {
    isLoading,
    shopInfo,
    error
  };

  return <RootContext.Provider value={value}>{children}</RootContext.Provider>;
};

export default RootProvider;

export const useRoot = () => {
  return useContext(RootContext);
};
