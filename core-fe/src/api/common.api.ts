import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from "../dashboard/lib/api-handler";
import { passParams } from "../dashboard/lib/pass-params";
import { ShopInfoDto } from "../dashboard/models/common/shop-data.model";

export class CommonApi {
  static async GetShop(instanceId: string) {
    const response = await <PERSON>piHand<PERSON><ShopInfoDto>({
      method: "GET",
      url: passParams("/common/shop-info", { instanceId })
    });
    return response;
  }
}
